import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import webpush from 'web-push';
import { startPriceMonitor } from './price-monitor.js';
import { checkAlerts } from './alert-handler.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// In-memory storage for alerts (MVP trade-off)
export let activeAlerts = [];

// Configure web-push
webpush.setVapidDetails(
  `mailto:${process.env.VAPID_EMAIL}`,
  process.env.VAPID_PUBLIC_KEY,
  process.env.VAPID_PRIVATE_KEY
);

// Middleware
app.use(cors());
app.use(express.json());

// Serve static files from frontend build
const frontendPath = path.join(__dirname, '../../frontend/dist');
app.use(express.static(frontendPath));

// API Routes
app.get('/api/vapid-public-key', (req, res) => {
  res.json({
    publicKey: process.env.VAPID_PUBLIC_KEY
  });
});

app.post('/api/set-alert', (req, res) => {
  try {
    const { coin, condition, price, pushSubscription } = req.body;

    // Validate input
    if (!coin || !condition || !price || !pushSubscription) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    if (!['ABOVE', 'BELOW'].includes(condition)) {
      return res.status(400).json({ error: 'Invalid condition. Must be ABOVE or BELOW' });
    }

    if (typeof price !== 'number' || price <= 0) {
      return res.status(400).json({ error: 'Price must be a positive number' });
    }

    // Create alert object
    const alert = {
      id: Date.now().toString(),
      coin: coin.toUpperCase(),
      condition,
      price,
      pushSubscription,
      createdAt: new Date().toISOString()
    };

    // Add to active alerts
    activeAlerts.push(alert);

    console.log(`✅ New alert set: ${coin.toUpperCase()} ${condition} $${price} (Total alerts: ${activeAlerts.length})`);

    res.json({ 
      success: true, 
      message: 'Alert set successfully',
      alertId: alert.id
    });

  } catch (error) {
    console.error('Error setting alert:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/alerts/count', (req, res) => {
  res.json({ count: activeAlerts.length });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    activeAlerts: activeAlerts.length
  });
});

// Serve React app for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(frontendPath, 'index.html'));
});

// Start the server
app.listen(PORT, () => {
  console.log(`🚀 PumpAlarm server running on port ${PORT}`);
  console.log(`📱 Frontend available at: http://localhost:${PORT}`);
  console.log(`🔔 VAPID configured: ${!!process.env.VAPID_PUBLIC_KEY}`);
  
  // Start price monitoring
  startPriceMonitor((priceData) => {
    checkAlerts(priceData, activeAlerts);
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully');
  process.exit(0);
});


