import webpush from 'web-push';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configure web-push
webpush.setVapidDetails(
  `mailto:${process.env.VAPID_EMAIL}`,
  process.env.VAPID_PUBLIC_KEY,
  process.env.VAPID_PRIVATE_KEY
);

export function checkAlerts(priceData, activeAlerts) {
  const { symbol, price } = priceData;
  
  // Find alerts that match this symbol
  const matchingAlerts = activeAlerts.filter(alert => {
    // Convert coin symbol to Binance format (e.g., BTC -> BTCUSDT)
    const binanceSymbol = alert.coin + 'USDT';
    return symbol === binanceSymbol;
  });

  if (matchingAlerts.length === 0) {
    return; // No alerts for this symbol
  }

  // Check each matching alert
  matchingAlerts.forEach(async (alert, index) => {
    let shouldTrigger = false;

    if (alert.condition === 'ABOVE' && price >= alert.price) {
      shouldTrigger = true;
    } else if (alert.condition === 'BELOW' && price <= alert.price) {
      shouldTrigger = true;
    }

    if (shouldTrigger) {
      console.log(`🚨 Alert triggered: ${alert.coin} ${alert.condition} $${alert.price} (Current: $${price})`);
      
      try {
        // Send push notification
        await sendPushNotification(alert, price);
        
        // Remove the alert from active alerts (one-shot alert)
        const alertIndex = activeAlerts.findIndex(a => a.id === alert.id);
        if (alertIndex > -1) {
          activeAlerts.splice(alertIndex, 1);
          console.log(`✅ Alert removed. Remaining alerts: ${activeAlerts.length}`);
        }
        
      } catch (error) {
        console.error('❌ Error sending push notification:', error);
        
        // If push notification fails, still remove the alert to prevent spam
        const alertIndex = activeAlerts.findIndex(a => a.id === alert.id);
        if (alertIndex > -1) {
          activeAlerts.splice(alertIndex, 1);
          console.log(`⚠️ Alert removed due to notification error. Remaining alerts: ${activeAlerts.length}`);
        }
      }
    }
  });
}

async function sendPushNotification(alert, currentPrice) {
  const { coin, condition, price, pushSubscription } = alert;
  
  const payload = JSON.stringify({
    title: '🚨 PumpAlarm Alert!',
    body: `${coin} has gone ${condition.toLowerCase()} your target of $${price.toFixed(2)}! Current price: $${currentPrice.toFixed(2)}`,
    data: {
      coin,
      condition,
      targetPrice: price,
      currentPrice,
      timestamp: new Date().toISOString()
    }
  });

  const options = {
    TTL: 60 * 60 * 24, // 24 hours
    vapidDetails: {
      subject: `mailto:${process.env.VAPID_EMAIL}`,
      publicKey: process.env.VAPID_PUBLIC_KEY,
      privateKey: process.env.VAPID_PRIVATE_KEY
    }
  };

  try {
    const result = await webpush.sendNotification(pushSubscription, payload, options);
    console.log('✅ Push notification sent successfully');
    return result;
  } catch (error) {
    console.error('❌ Push notification failed:', error);
    
    // If the subscription is invalid, we should handle it gracefully
    if (error.statusCode === 410 || error.statusCode === 404) {
      console.log('📱 Push subscription is no longer valid');
    }
    
    throw error;
  }
}
