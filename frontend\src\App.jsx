import React, { useState, useEffect } from 'react';

function App() {
  const [coin, setCoin] = useState('');
  const [condition, setCondition] = useState('ABOVE');
  const [price, setPrice] = useState('');
  const [isSupported, setIsSupported] = useState(false);
  const [permission, setPermission] = useState('default');
  const [subscription, setSubscription] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [alertType, setAlertType] = useState('NORMAL');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [vibrationEnabled, setVibrationEnabled] = useState(true);

  useEffect(() => {
    // Check if push notifications are supported
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      setIsSupported(true);
      initializeServiceWorker();
    } else {
      setMessage('Push notifications are not supported in this browser.');
    }
  }, []);

  const initializeServiceWorker = async () => {
    try {
      // Register service worker
      const registration = await navigator.serviceWorker.register('/service-worker.js');
      console.log('Service Worker registered:', registration);

      // Check current permission
      setPermission(Notification.permission);

      // If permission is granted, get existing subscription
      if (Notification.permission === 'granted') {
        const existingSubscription = await registration.pushManager.getSubscription();
        if (existingSubscription) {
          setSubscription(existingSubscription);
        }
      }
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      setMessage('Failed to register service worker.');
    }
  };

  const requestNotificationPermission = async () => {
    try {
      const permission = await Notification.requestPermission();
      setPermission(permission);

      if (permission === 'granted') {
        await subscribeToPush();
        setMessage('Notifications enabled! You can now set alerts.');
      } else {
        setMessage('Notifications denied. Please enable them to receive alerts.');
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      setMessage('Error requesting notification permission.');
    }
  };

  const subscribeToPush = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      
      // Get VAPID public key from backend
      const response = await fetch('/api/vapid-public-key');
      const { publicKey } = await response.json();

      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(publicKey)
      });

      setSubscription(subscription);
      console.log('Push subscription:', subscription);
    } catch (error) {
      console.error('Error subscribing to push:', error);
      setMessage('Error setting up push notifications.');
    }
  };

  const urlBase64ToUint8Array = (base64String) => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!subscription) {
      setMessage('Please enable notifications first.');
      return;
    }

    if (!coin || !price) {
      setMessage('Please fill in all fields.');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/set-alert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          coin: coin.toUpperCase(),
          condition,
          price: parseFloat(price),
          pushSubscription: subscription,
          alertType,
          soundEnabled,
          vibrationEnabled
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage(`Alert set! You'll be notified when ${coin.toUpperCase()} goes ${condition.toLowerCase()} $${price}`);
        setCoin('');
        setPrice('');
      } else {
        setMessage(result.error || 'Failed to set alert.');
      }
    } catch (error) {
      console.error('Error setting alert:', error);
      setMessage('Error setting alert. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <h1 style={styles.title}>🚨 PumpAlarm</h1>
        <p style={styles.subtitle}>Get notified when crypto prices hit your targets</p>

        {!isSupported && (
          <div style={styles.error}>
            Push notifications are not supported in this browser.
          </div>
        )}

        {isSupported && permission !== 'granted' && (
          <div style={styles.permissionSection}>
            <p style={styles.permissionText}>
              Enable notifications to receive price alerts
            </p>
            <button 
              onClick={requestNotificationPermission}
              style={styles.permissionButton}
            >
              Enable Notifications
            </button>
          </div>
        )}

        {permission === 'granted' && (
          <form onSubmit={handleSubmit} style={styles.form}>
            <div style={styles.inputGroup}>
              <label style={styles.label}>Cryptocurrency Symbol</label>
              <input
                type="text"
                value={coin}
                onChange={(e) => setCoin(e.target.value)}
                placeholder="e.g., BTC, ETH, SOL"
                style={styles.input}
                required
              />
            </div>

            <div style={styles.inputGroup}>
              <label style={styles.label}>Condition</label>
              <select
                value={condition}
                onChange={(e) => setCondition(e.target.value)}
                style={styles.select}
              >
                <option value="ABOVE">Price goes above</option>
                <option value="BELOW">Price goes below</option>
              </select>
            </div>

            <div style={styles.inputGroup}>
              <label style={styles.label}>Target Price (USD)</label>
              <input
                type="number"
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                placeholder="0.00"
                step="0.01"
                min="0"
                style={styles.input}
                required
              />
            </div>

            <div style={styles.inputGroup}>
              <label style={styles.label}>Alert Type</label>
              <select
                value={alertType}
                onChange={(e) => setAlertType(e.target.value)}
                style={styles.select}
              >
                <option value="NORMAL">🔔 Normal Alert</option>
                <option value="URGENT">⚠️ Urgent Alert</option>
                <option value="EMERGENCY">🚨 Emergency/SOS Alert</option>
              </select>
            </div>

            <div style={styles.checkboxGroup}>
              <label style={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  checked={soundEnabled}
                  onChange={(e) => setSoundEnabled(e.target.checked)}
                  style={styles.checkbox}
                />
                🔊 Enable Sound Alert
              </label>

              <label style={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  checked={vibrationEnabled}
                  onChange={(e) => setVibrationEnabled(e.target.checked)}
                  style={styles.checkbox}
                />
                📳 Enable Vibration (Mobile)
              </label>
            </div>

            <button 
              type="submit" 
              disabled={isLoading}
              style={{
                ...styles.submitButton,
                opacity: isLoading ? 0.6 : 1
              }}
            >
              {isLoading ? 'Setting Alert...' : 'Set Alert'}
            </button>
          </form>
        )}

        {message && (
          <div style={{
            ...styles.message,
            backgroundColor: message.includes('Error') || message.includes('Failed') || message.includes('denied') 
              ? '#fee' : '#efe',
            color: message.includes('Error') || message.includes('Failed') || message.includes('denied')
              ? '#c33' : '#363'
          }}>
            {message}
          </div>
        )}

        <div style={styles.footer}>
          <p style={styles.footerText}>
            ⚠️ MVP Notice: Alerts are stored in memory and will be lost if the server restarts.
          </p>
        </div>
      </div>
    </div>
  );
}

const styles = {
  container: {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: '12px',
    padding: '40px',
    boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
    maxWidth: '400px',
    width: '100%',
  },
  title: {
    fontSize: '2.5rem',
    fontWeight: 'bold',
    textAlign: 'center',
    margin: '0 0 10px 0',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
  },
  subtitle: {
    textAlign: 'center',
    color: '#666',
    marginBottom: '30px',
  },
  error: {
    backgroundColor: '#fee',
    color: '#c33',
    padding: '15px',
    borderRadius: '8px',
    textAlign: 'center',
    marginBottom: '20px',
  },
  permissionSection: {
    textAlign: 'center',
    marginBottom: '30px',
  },
  permissionText: {
    color: '#666',
    marginBottom: '15px',
  },
  permissionButton: {
    backgroundColor: '#667eea',
    color: 'white',
    border: 'none',
    padding: '12px 24px',
    borderRadius: '8px',
    fontSize: '16px',
    cursor: 'pointer',
    fontWeight: '500',
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    gap: '20px',
  },
  inputGroup: {
    display: 'flex',
    flexDirection: 'column',
  },
  label: {
    fontSize: '14px',
    fontWeight: '500',
    marginBottom: '8px',
    color: '#333',
  },
  input: {
    padding: '12px',
    border: '2px solid #e1e5e9',
    borderRadius: '8px',
    fontSize: '16px',
    transition: 'border-color 0.2s',
  },
  select: {
    padding: '12px',
    border: '2px solid #e1e5e9',
    borderRadius: '8px',
    fontSize: '16px',
    backgroundColor: 'white',
  },
  submitButton: {
    backgroundColor: '#667eea',
    color: 'white',
    border: 'none',
    padding: '15px',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'background-color 0.2s',
  },
  message: {
    padding: '15px',
    borderRadius: '8px',
    textAlign: 'center',
    marginTop: '20px',
    fontSize: '14px',
  },
  footer: {
    marginTop: '30px',
    paddingTop: '20px',
    borderTop: '1px solid #eee',
  },
  footerText: {
    fontSize: '12px',
    color: '#999',
    textAlign: 'center',
    margin: 0,
  },
  checkboxGroup: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
  },
  checkboxLabel: {
    display: 'flex',
    alignItems: 'center',
    fontSize: '14px',
    color: '#333',
    cursor: 'pointer',
  },
  checkbox: {
    marginRight: '8px',
    transform: 'scale(1.2)',
  },
};

export default App;
